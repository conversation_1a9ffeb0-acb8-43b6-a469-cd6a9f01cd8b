<template>
  <div class="flex gap-10px">
    <Button
      :disabled="updateButton.disabled"
      @click="handleUpdate"
    >
      {{ updateButton.text }}
    </Button>
    <Button
      :disabled="restartButton.disabled"
      @click="handleRestart"
    >
      {{ restartButton.text }}
    </Button>
    <Button
      :disabled="disableButton.disabled"
      @click="handleDisable"
    >
      {{ disableButton.text }}
    </Button>
  </div>
</template>

<script setup lang="ts">
import { Button } from 'ant-design-vue';
import { computed } from 'vue';
import type { InstanceConfigItem } from '../../../api';
import { pendingStateType, workStateType } from './type.data';

const props = withDefaults(defineProps<{
  item: InstanceConfigItem;
}>(), {
  item: () => ({}),
});

const emit = defineEmits<{
  update: [instanceId: number, isCancel: boolean];
  restart: [instanceId: number, isCancel: boolean];
  disable: [instanceId: number, isCancel: boolean];
}>();

/**
 * 按钮状态配置常量
 * 定义了不同操作按钮在各种状态下的行为规则
 */
const BUTTON_STATES = {
  update: {
    // 禁用更新按钮的工作状态：离线、重启中、更新中
    disabledWorkStates: [workStateType.Offline, workStateType.Restarting, workStateType.Updating],
    // 禁用更新按钮的等待状态：等待重启、等待重启+禁用
    disabledPendingStates: [pendingStateType.WaitRestart, pendingStateType.WaitRestartAndDisable],
    // 显示"取消更新"的等待状态
    cancelStates: [pendingStateType.WaitUpdate, pendingStateType.WaitUpdateAndDisable],
    // 显示"预约更新"的等待状态
    scheduleStates: [pendingStateType.Null, pendingStateType.WaitDisable],
  },
  restart: {
    // 禁用重启按钮的工作状态：离线、重启中
    disabledWorkStates: [workStateType.Offline, workStateType.Restarting],
    // 显示"取消重启"的等待状态
    cancelStates: [pendingStateType.WaitRestart, pendingStateType.WaitRestartAndDisable],
    // 可以进行重启操作的工作状态
    scheduleWorkStates: [workStateType.Checking, workStateType.Updating],
    // 不显示"预约重启"的等待状态
    excludeScheduleStates: [pendingStateType.WaitUpdate, pendingStateType.WaitDisable],
  },
  disable: {
    // 禁用禁用按钮的工作状态：重启中
    disabledWorkStates: [workStateType.Restarting],
    // 显示"取消禁用"的等待状态
    cancelStates: [
      pendingStateType.WaitDisable,
      pendingStateType.WaitUpdateAndDisable,
      pendingStateType.WaitRestartAndDisable,
    ],
    // 显示"预约禁用"的等待状态
    scheduleStates: [pendingStateType.WaitUpdate, pendingStateType.WaitRestart],
  },
} as const;

/**
 * 更新按钮状态计算
 * 根据实例的工作状态和等待状态计算按钮的禁用状态和显示文本
 */
const updateButton = computed(() => {
  const { workState, pendingState } = props.item;
  const { disabledWorkStates, disabledPendingStates, cancelStates, scheduleStates } = BUTTON_STATES.update;

  // 计算是否禁用：工作状态或等待状态满足禁用条件
  const disabled = disabledWorkStates.includes(workState!) || disabledPendingStates.includes(pendingState!);

  // 计算按钮文本
  let text = '更新';
  if (workState === workStateType.Checking && cancelStates.includes(pendingState!)) {
    text = '取消更新';
  } else if (workState === workStateType.Checking && scheduleStates.includes(pendingState!)) {
    text = '预约更新';
  }

  return { disabled, text };
});

/**
 * 重启按钮状态计算
 */
const restartButton = computed(() => {
  const { workState, pendingState } = props.item;
  const { disabledWorkStates, cancelStates, scheduleWorkStates, excludeScheduleStates } = BUTTON_STATES.restart;

  const disabled = disabledWorkStates.includes(workState!);

  let text = '重启';
  if (scheduleWorkStates.includes(workState!) && cancelStates.includes(pendingState!)) {
    text = '取消重启';
  } else if (scheduleWorkStates.includes(workState!) && !excludeScheduleStates.includes(pendingState!)) {
    text = '预约重启';
  }

  return { disabled, text };
});

/**
 * 禁用/启用按钮状态计算
 */
const disableButton = computed(() => {
  const { workState, pendingState, disabled: itemDisabled } = props.item;
  const { disabledWorkStates, cancelStates, scheduleStates } = BUTTON_STATES.disable;

  const disabled = disabledWorkStates.includes(workState!);

  let text = '禁用';
  if (itemDisabled) {
    text = '启用';
  } else if (cancelStates.includes(pendingState!)) {
    text = '取消禁用';
  } else if (workState === workStateType.Checking && scheduleStates.includes(pendingState!)) {
    text = '预约禁用';
  }

  return { disabled, text };
});

/**
 * 事件处理函数
 */

/** 处理更新按钮点击 */
function handleUpdate() {
  const { workState, pendingState } = props.item;
  const isCancel = workState === workStateType.Checking
    && BUTTON_STATES.update.cancelStates.includes(pendingState!);
  emit('update', props.item.id, isCancel);
}

/** 处理重启按钮点击 */
function handleRestart() {
  const { workState, pendingState } = props.item;
  const isCancel = BUTTON_STATES.restart.scheduleWorkStates.includes(workState!)
    && BUTTON_STATES.restart.cancelStates.includes(pendingState!);
  emit('restart', props.item.id, isCancel);
}

/** 处理禁用/启用按钮点击 */
function handleDisable() {
  emit('disable', props.item.id, props.item.disabled!);
}
</script>
